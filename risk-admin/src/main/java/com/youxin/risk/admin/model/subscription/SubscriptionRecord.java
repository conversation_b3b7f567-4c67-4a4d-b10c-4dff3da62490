package com.youxin.risk.admin.model.subscription;

import com.youxin.risk.admin.model.BaseModel;

import java.util.Date;

/**
 * 策略监控订阅记录实体类
 * 
 * <AUTHOR> Assistant
 */
public class SubscriptionRecord extends BaseModel {

    /**
     * 企业微信userid
     */
    private String userId;

    /**
     * 策略类型标识
     */
    private String strategyType;

    /**
     * 监控项唯一ID
     */
    private String monitorId;

    /**
     * 推送频率(分钟)。固定为60分钟
     */
    private Integer frequencyMinutes;

    /**
     * 总共需要推送的次数
     */
    private Integer totalPushes;

    /**
     * 已经推送的次数
     */
    private Integer sentPushes;

    /**
     * 是否激活 (1:是, 0:否)
     */
    private Integer isActive;

    /**
     * 上次通知时间
     */
    private Date lastNotifiedAt;

    public SubscriptionRecord() {
        this.frequencyMinutes = 60;
        this.totalPushes = 3;
        this.sentPushes = 0;
        this.isActive = 1;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getStrategyType() {
        return strategyType;
    }

    public void setStrategyType(String strategyType) {
        this.strategyType = strategyType;
    }

    public String getMonitorId() {
        return monitorId;
    }

    public void setMonitorId(String monitorId) {
        this.monitorId = monitorId;
    }

    public Integer getFrequencyMinutes() {
        return frequencyMinutes;
    }

    public void setFrequencyMinutes(Integer frequencyMinutes) {
        this.frequencyMinutes = frequencyMinutes;
    }

    public Integer getTotalPushes() {
        return totalPushes;
    }

    public void setTotalPushes(Integer totalPushes) {
        this.totalPushes = totalPushes;
    }

    public Integer getSentPushes() {
        return sentPushes;
    }

    public void setSentPushes(Integer sentPushes) {
        this.sentPushes = sentPushes;
    }

    public Integer getIsActive() {
        return isActive;
    }

    public void setIsActive(Integer isActive) {
        this.isActive = isActive;
    }

    public Date getLastNotifiedAt() {
        return lastNotifiedAt;
    }

    public void setLastNotifiedAt(Date lastNotifiedAt) {
        this.lastNotifiedAt = lastNotifiedAt;
    }

    @Override
    public String toString() {
        return "SubscriptionRecord{" +
                "id=" + getId() +
                ", userId='" + userId + '\'' +
                ", strategyType='" + strategyType + '\'' +
                ", monitorId='" + monitorId + '\'' +
                ", frequencyMinutes=" + frequencyMinutes +
                ", totalPushes=" + totalPushes +
                ", sentPushes=" + sentPushes +
                ", isActive=" + isActive +
                ", lastNotifiedAt=" + lastNotifiedAt +
                ", createTime=" + getCreateTime() +
                ", updateTime=" + getUpdateTime() +
                '}';
    }
}

package com.youxin.risk.admin.service.subscription;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.model.subscription.MonitorConfig;
import com.youxin.risk.admin.model.subscription.StrategyMonitorMapping;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.SLSLinkGenerator;
import com.youxin.risk.commons.utils.service.NacosConfiguration;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 策略监控配置服务
 * 
 * <AUTHOR> Assistant
 */
@Service
public class StrategyMonitorConfigService {

    private static final Logger logger = LoggerFactory.getLogger(StrategyMonitorConfigService.class);

    private static final String CONFIG_DATA_ID = "strategy-monitor-mapping";
    private static final String CONFIG_GROUP = "STRATEGY_CENTER";

    /**
     * 根据策略类型获取监控配置
     * 
     * @param strategyType 策略类型
     * @return 策略监控映射配置
     */
    public StrategyMonitorMapping getStrategyMonitorMapping(String strategyType) {
        try {
            String configContent = NacosConfiguration.getConfig(CONFIG_DATA_ID, CONFIG_GROUP);
            if (configContent == null || configContent.trim().isEmpty()) {
                LoggerProxy.warn("getStrategyMonitorMapping", logger, "策略监控映射配置为空, strategyType={}", strategyType);
                return null;
            }

            JSONObject configJson = JSON.parseObject(configContent);
            JSONObject strategyConfig = configJson.getJSONObject(strategyType);
            
            if (strategyConfig == null) {
                LoggerProxy.warn("getStrategyMonitorMapping", logger, "未找到策略类型配置, strategyType={}", strategyType);
                return null;
            }

            StrategyMonitorMapping mapping = new StrategyMonitorMapping();
            mapping.setName(strategyConfig.getString("name"));
            
            List<MonitorConfig> monitors = new ArrayList<>();
            if (strategyConfig.containsKey("monitors")) {
                List<Map<String, Object>> monitorList = (List<Map<String, Object>>) strategyConfig.get("monitors");
                for (Map<String, Object> monitorMap : monitorList) {
                    MonitorConfig monitor = new MonitorConfig();
                    monitor.setId((String) monitorMap.get("id"));
                    monitor.setName((String) monitorMap.get("name"));
                    monitor.setUrl((String) monitorMap.get("url"));
                    monitor.setEventCode((String) monitorMap.get("eventCode"));

                    // 设置是否使用动态链接
                    Boolean useDynamicLink = (Boolean) monitorMap.get("useDynamicLink");
                    monitor.setUseDynamicLink(useDynamicLink);

                    monitors.add(monitor);
                }
            }
            mapping.setMonitors(monitors);

            LoggerProxy.info("getStrategyMonitorMapping", logger, "获取策略监控配置成功, strategyType={}, monitors={}", 
                    strategyType, monitors.size());
            return mapping;

        } catch (Exception e) {
            LoggerProxy.error("getStrategyMonitorMapping", logger, "获取策略监控配置失败, strategyType=" + strategyType, e);
            return null;
        }
    }

    /**
     * 获取所有策略监控配置
     * 
     * @return 所有策略监控配置的映射
     */
    public Map<String, StrategyMonitorMapping> getAllStrategyMonitorMappings() {
        try {
            String configContent = NacosConfiguration.getConfig(CONFIG_DATA_ID, CONFIG_GROUP);
            if (configContent == null || configContent.trim().isEmpty()) {
                LoggerProxy.warn("getAllStrategyMonitorMappings", logger, "策略监控映射配置为空");
                return null;
            }

            JSONObject configJson = JSON.parseObject(configContent);
            Map<String, StrategyMonitorMapping> mappings = new java.util.HashMap<>();
            
            for (String strategyType : configJson.keySet()) {
                StrategyMonitorMapping mapping = getStrategyMonitorMapping(strategyType);
                if (mapping != null) {
                    mappings.put(strategyType, mapping);
                }
            }

            LoggerProxy.info("getAllStrategyMonitorMappings", logger, "获取所有策略监控配置成功, count={}", mappings.size());
            return mappings;

        } catch (Exception e) {
            LoggerProxy.error("getAllStrategyMonitorMappings", logger, "获取所有策略监控配置失败", e);
            return null;
        }
    }

    /**
     * 检查策略类型是否已配置
     *
     * @param strategyType 策略类型
     * @return 是否已配置
     */
    public boolean isStrategyTypeConfigured(String strategyType) {
        StrategyMonitorMapping mapping = getStrategyMonitorMapping(strategyType);
        return mapping != null && mapping.getMonitors() != null && !mapping.getMonitors().isEmpty();
    }

    /**
     * 为监控配置生成动态链接
     *
     * @param monitor 监控配置
     * @return 生成的监控链接，如果生成失败则返回静态URL
     */
    public String generateMonitorLink(MonitorConfig monitor) {
        if (monitor == null) {
            LoggerProxy.warn("generateMonitorLink", logger, "监控配置为空");
            return null;
        }

        // 如果启用动态链接且有eventCode，则生成动态链接
        if (monitor.isDynamicLinkEnabled()) {
            try {
                LoggerProxy.info("generateMonitorLink", logger,
                        "开始生成动态监控链接, eventCode={}, monitorId={}",
                        monitor.getEventCode(), monitor.getId());

                String dynamicLink = SLSLinkGenerator.getShareableLinkByEventCode(monitor.getEventCode());

                if (StringUtils.isNotEmpty(dynamicLink)) {
                    LoggerProxy.info("generateMonitorLink", logger,
                            "动态监控链接生成成功, eventCode={}, link={}",
                            monitor.getEventCode(), dynamicLink);
                    return dynamicLink;
                } else {
                    LoggerProxy.warn("generateMonitorLink", logger,
                            "动态监控链接生成失败，回退到静态URL, eventCode={}", monitor.getEventCode());
                }
            } catch (Exception e) {
                LoggerProxy.error("generateMonitorLink", logger,
                        "生成动态监控链接异常，回退到静态URL, eventCode=" + monitor.getEventCode(), e);
            }
        }

        // 回退到静态URL
        if (StringUtils.isNotEmpty(monitor.getUrl())) {
            LoggerProxy.info("generateMonitorLink", logger,
                    "使用静态监控链接, monitorId={}, url={}", monitor.getId(), monitor.getUrl());
            return monitor.getUrl();
        }

        LoggerProxy.warn("generateMonitorLink", logger,
                "无法生成监控链接，eventCode和静态URL都不可用, monitorId={}", monitor.getId());
        return null;
    }

    /**
     * 批量生成监控链接
     *
     * @param monitors 监控配置列表
     * @return 监控配置与链接的映射
     */
    public Map<String, String> batchGenerateMonitorLinks(List<MonitorConfig> monitors) {
        Map<String, String> linkMap = new java.util.HashMap<>();

        if (monitors == null || monitors.isEmpty()) {
            return linkMap;
        }

        for (MonitorConfig monitor : monitors) {
            String link = generateMonitorLink(monitor);
            if (StringUtils.isNotEmpty(link)) {
                linkMap.put(monitor.getId(), link);
            }
        }

        LoggerProxy.info("batchGenerateMonitorLinks", logger,
                "批量生成监控链接完成, totalMonitors={}, successCount={}",
                monitors.size(), linkMap.size());

        return linkMap;
    }

    /**
     * 刷新监控链接（重新生成动态链接）
     *
     * @param eventCode 事件代码
     * @return 新生成的监控链接
     */
    public String refreshMonitorLink(String eventCode) {
        if (StringUtils.isEmpty(eventCode)) {
            LoggerProxy.warn("refreshMonitorLink", logger, "事件代码为空");
            return null;
        }

        try {
            LoggerProxy.info("refreshMonitorLink", logger, "开始刷新监控链接, eventCode={}", eventCode);

            String refreshedLink = SLSLinkGenerator.getShareableLinkByEventCode(eventCode);

            if (StringUtils.isNotEmpty(refreshedLink)) {
                LoggerProxy.info("refreshMonitorLink", logger,
                        "监控链接刷新成功, eventCode={}, link={}", eventCode, refreshedLink);
                return refreshedLink;
            } else {
                LoggerProxy.warn("refreshMonitorLink", logger,
                        "监控链接刷新失败, eventCode={}", eventCode);
                return null;
            }
        } catch (Exception e) {
            LoggerProxy.error("refreshMonitorLink", logger,
                    "刷新监控链接异常, eventCode=" + eventCode, e);
            return null;
        }
    }
}

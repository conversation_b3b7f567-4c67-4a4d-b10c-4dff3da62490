package com.youxin.risk.admin.model.subscription;

import java.util.List;

/**
 * 策略监控映射配置实体类
 * 
 * <AUTHOR> Assistant
 */
public class StrategyMonitorMapping {

    /**
     * 策略类型名称
     */
    private String name;

    /**
     * 监控项列表
     */
    private List<MonitorConfig> monitors;

    public StrategyMonitorMapping() {
    }

    public StrategyMonitorMapping(String name, List<MonitorConfig> monitors) {
        this.name = name;
        this.monitors = monitors;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<MonitorConfig> getMonitors() {
        return monitors;
    }

    public void setMonitors(List<MonitorConfig> monitors) {
        this.monitors = monitors;
    }

    @Override
    public String toString() {
        return "StrategyMonitorMapping{" +
                "name='" + name + '\'' +
                ", monitors=" + monitors +
                '}';
    }
}

package com.youxin.risk.admin.controller;

import com.youxin.risk.admin.dto.JsonResult;
import com.youxin.risk.admin.model.subscription.MonitorConfig;
import com.youxin.risk.admin.model.subscription.StrategyMonitorMapping;
import com.youxin.risk.admin.model.subscription.SubscriptionRecord;
import com.youxin.risk.admin.service.subscription.StrategyMonitorConfigService;
import com.youxin.risk.admin.service.subscription.StrategySubscriptionService;
import com.youxin.risk.admin.service.wechat.WechatMessageService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.SubscriptionUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 策略监控订阅控制器
 * 
 * <AUTHOR> Assistant
 */
@Api(tags = "策略监控订阅管理")
@RestController
@RequestMapping("/admin/strategy/subscription")
public class StrategySubscriptionController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(StrategySubscriptionController.class);

    @Autowired
    private StrategySubscriptionService strategySubscriptionService;

    @Autowired
    private StrategyMonitorConfigService strategyMonitorConfigService;

    @Autowired
    private WechatMessageService wechatMessageService;

    /**
     * 创建策略监控订阅
     * 
     * @param userId 用户ID
     * @param strategyType 策略类型
     * @return 创建结果
     */
    @ApiOperation("创建策略监控订阅")
    @PostMapping("/create")
    public JsonResult<Boolean> createSubscription(
            @ApiParam(value = "用户ID", required = true) @RequestParam String userId,
            @ApiParam(value = "策略类型", required = true) @RequestParam String strategyType) {
        
        try {
            LoggerProxy.info("createSubscription", logger, 
                    "接收到创建订阅请求, userId={}, strategyType={}", userId, strategyType);

            // 参数验证
            if (!SubscriptionUtils.validateSubscriptionParams(userId, strategyType)) {
                return JsonResult.error("参数不能为空");
            }

            // 检查策略类型是否已配置
            if (!strategyMonitorConfigService.isStrategyTypeConfigured(strategyType)) {
                return JsonResult.error("策略类型未配置监控项: " + strategyType);
            }

            // 创建订阅
            boolean success = strategySubscriptionService.createStrategySubscription(userId, strategyType);
            
            if (success) {
                LoggerProxy.info("createSubscription", logger, 
                        "创建订阅成功, userId={}, strategyType={}", userId, strategyType);
                return JsonResult.success(true);
            } else {
                LoggerProxy.warn("createSubscription", logger, 
                        "创建订阅失败, userId={}, strategyType={}", userId, strategyType);
                return JsonResult.error("创建订阅失败");
            }

        } catch (Exception e) {
            LoggerProxy.error("createSubscription", logger, 
                    "创建订阅异常, userId=" + userId + ", strategyType=" + strategyType, e);
            return JsonResult.error("创建订阅异常: " + e.getMessage());
        }
    }

    /**
     * 查询用户订阅记录
     * 
     * @param userId 用户ID
     * @param strategyType 策略类型（可选）
     * @return 订阅记录列表
     */
    @ApiOperation("查询用户订阅记录")
    @GetMapping("/list")
    public JsonResult<List<SubscriptionRecord>> getUserSubscriptions(
            @ApiParam(value = "用户ID", required = true) @RequestParam String userId,
            @ApiParam(value = "策略类型") @RequestParam(required = false) String strategyType) {
        
        try {
            LoggerProxy.info("getUserSubscriptions", logger, 
                    "查询用户订阅记录, userId={}, strategyType={}", userId, strategyType);

            if (userId == null || userId.trim().isEmpty()) {
                return JsonResult.error("用户ID不能为空");
            }

            List<SubscriptionRecord> subscriptions = strategySubscriptionService.getUserSubscriptions(userId, strategyType);
            
            LoggerProxy.info("getUserSubscriptions", logger, 
                    "查询用户订阅记录完成, userId={}, strategyType={}, count={}", 
                    userId, strategyType, subscriptions.size());
            
            return JsonResult.success(subscriptions);

        } catch (Exception e) {
            LoggerProxy.error("getUserSubscriptions", logger, 
                    "查询用户订阅记录异常, userId=" + userId + ", strategyType=" + strategyType, e);
            return JsonResult.error("查询订阅记录异常: " + e.getMessage());
        }
    }

    /**
     * 查询待推送的订阅记录
     * 
     * @return 待推送的订阅记录列表
     */
    @ApiOperation("查询待推送的订阅记录")
    @GetMapping("/pending")
    public JsonResult<List<SubscriptionRecord>> getPendingSubscriptions() {
        try {
            LoggerProxy.info("getPendingSubscriptions", logger, "查询待推送订阅记录");

            List<SubscriptionRecord> pendingSubscriptions = strategySubscriptionService.getPendingSubscriptions();
            
            LoggerProxy.info("getPendingSubscriptions", logger, 
                    "查询待推送订阅记录完成, count={}", pendingSubscriptions.size());
            
            return JsonResult.success(pendingSubscriptions);

        } catch (Exception e) {
            LoggerProxy.error("getPendingSubscriptions", logger, "查询待推送订阅记录异常", e);
            return JsonResult.error("查询待推送订阅记录异常: " + e.getMessage());
        }
    }

    /**
     * 检查策略类型配置
     * 
     * @param strategyType 策略类型
     * @return 是否已配置
     */
    @ApiOperation("检查策略类型配置")
    @GetMapping("/check-config")
    public JsonResult<Boolean> checkStrategyConfig(
            @ApiParam(value = "策略类型", required = true) @RequestParam String strategyType) {
        
        try {
            LoggerProxy.info("checkStrategyConfig", logger, "检查策略类型配置, strategyType={}", strategyType);

            if (strategyType == null || strategyType.trim().isEmpty()) {
                return JsonResult.error("策略类型不能为空");
            }

            boolean configured = strategyMonitorConfigService.isStrategyTypeConfigured(strategyType);
            
            LoggerProxy.info("checkStrategyConfig", logger, 
                    "检查策略类型配置完成, strategyType={}, configured={}", strategyType, configured);
            
            return JsonResult.success(configured);

        } catch (Exception e) {
            LoggerProxy.error("checkStrategyConfig", logger, 
                    "检查策略类型配置异常, strategyType=" + strategyType, e);
            return JsonResult.error("检查策略类型配置异常: " + e.getMessage());
        }
    }

    /**
     * 手动触发推送（用于测试）
     * 
     * @return 推送结果
     */
    @ApiOperation("手动触发推送")
    @PostMapping("/manual-push")
    public JsonResult<String> manualPush() {
        try {
            LoggerProxy.info("manualPush", logger, "手动触发推送");

            // 获取待推送记录
            List<SubscriptionRecord> pendingSubscriptions = strategySubscriptionService.getPendingSubscriptions();
            
            if (pendingSubscriptions.isEmpty()) {
                return JsonResult.success("没有需要推送的记录");
            }

            // 这里可以调用定时任务的逻辑
            String result = String.format("找到 %d 条待推送记录，请查看日志了解详细执行情况", pendingSubscriptions.size());
            
            LoggerProxy.info("manualPush", logger, "手动触发推送完成, pendingCount={}", pendingSubscriptions.size());
            
            return JsonResult.success(result);

        } catch (Exception e) {
            LoggerProxy.error("manualPush", logger, "手动触发推送异常", e);
            return JsonResult.error("手动触发推送异常: " + e.getMessage());
        }
    }

    /**
     * 刷新用户的监控链接
     *
     * @param userId 用户ID
     * @param strategyType 策略类型（可选）
     * @return 刷新结果
     */
    @ApiOperation("刷新监控链接")
    @PostMapping("/refresh-links")
    public JsonResult<Map<String, String>> refreshMonitorLinks(
            @ApiParam(value = "用户ID", required = true) @RequestParam String userId,
            @ApiParam(value = "策略类型") @RequestParam(required = false) String strategyType) {

        try {
            LoggerProxy.info("refreshMonitorLinks", logger,
                    "接收到刷新监控链接请求, userId={}, strategyType={}", userId, strategyType);

            if (StringUtils.isEmpty(userId)) {
                return JsonResult.error("用户ID不能为空");
            }

            Map<String, String> refreshedLinks = new HashMap<>();

            if (StringUtils.isNotEmpty(strategyType)) {
                // 刷新指定策略类型的链接
                refreshedLinks = refreshLinksForStrategy(userId, strategyType);
            } else {
                // 刷新用户所有订阅的链接
                refreshedLinks = refreshAllLinksForUser(userId);
            }

            // 发送刷新后的链接给用户
            boolean sent = wechatMessageService.sendRefreshedMonitorLinks(userId, refreshedLinks);

            if (sent) {
                LoggerProxy.info("refreshMonitorLinks", logger,
                        "刷新监控链接成功, userId={}, strategyType={}, linkCount={}",
                        userId, strategyType, refreshedLinks.size());
                return JsonResult.success(refreshedLinks);
            } else {
                LoggerProxy.warn("refreshMonitorLinks", logger,
                        "发送刷新链接消息失败, userId={}, strategyType={}", userId, strategyType);
                return JsonResult.error("发送刷新链接消息失败");
            }

        } catch (Exception e) {
            LoggerProxy.error("refreshMonitorLinks", logger,
                    "刷新监控链接异常, userId=" + userId + ", strategyType=" + strategyType, e);
            return JsonResult.error("刷新监控链接异常: " + e.getMessage());
        }
    }

    /**
     * 根据事件代码刷新单个监控链接
     *
     * @param eventCode 事件代码
     * @return 刷新后的链接
     */
    @ApiOperation("根据事件代码刷新监控链接")
    @PostMapping("/refresh-link-by-event")
    public JsonResult<String> refreshLinkByEventCode(
            @ApiParam(value = "事件代码", required = true) @RequestParam String eventCode) {

        try {
            LoggerProxy.info("refreshLinkByEventCode", logger, "刷新监控链接, eventCode={}", eventCode);

            if (StringUtils.isEmpty(eventCode)) {
                return JsonResult.error("事件代码不能为空");
            }

            String refreshedLink = strategyMonitorConfigService.refreshMonitorLink(eventCode);

            if (StringUtils.isNotEmpty(refreshedLink)) {
                LoggerProxy.info("refreshLinkByEventCode", logger,
                        "刷新监控链接成功, eventCode={}, link={}", eventCode, refreshedLink);
                return JsonResult.success(refreshedLink);
            } else {
                LoggerProxy.warn("refreshLinkByEventCode", logger,
                        "刷新监控链接失败, eventCode={}", eventCode);
                return JsonResult.error("刷新监控链接失败");
            }

        } catch (Exception e) {
            LoggerProxy.error("refreshLinkByEventCode", logger,
                    "刷新监控链接异常, eventCode=" + eventCode, e);
            return JsonResult.error("刷新监控链接异常: " + e.getMessage());
        }
    }

    /**
     * 刷新指定策略类型的监控链接
     */
    private Map<String, String> refreshLinksForStrategy(String userId, String strategyType) {
        Map<String, String> refreshedLinks = new HashMap<>();

        StrategyMonitorMapping mapping = strategyMonitorConfigService.getStrategyMonitorMapping(strategyType);
        if (mapping != null && mapping.getMonitors() != null) {
            for (MonitorConfig monitor : mapping.getMonitors()) {
                String refreshedLink = strategyMonitorConfigService.generateMonitorLink(monitor);
                refreshedLinks.put(monitor.getName(), refreshedLink);
            }
        }

        return refreshedLinks;
    }

    /**
     * 刷新用户所有订阅的监控链接
     */
    private Map<String, String> refreshAllLinksForUser(String userId) {
        Map<String, String> refreshedLinks = new HashMap<>();

        // 获取用户所有订阅记录
        List<SubscriptionRecord> subscriptions = strategySubscriptionService.getUserSubscriptions(userId, null);

        // 按策略类型分组
        Map<String, List<SubscriptionRecord>> strategyGroups = subscriptions.stream()
                .collect(java.util.stream.Collectors.groupingBy(SubscriptionRecord::getStrategyType));

        for (String strategyType : strategyGroups.keySet()) {
            Map<String, String> strategyLinks = refreshLinksForStrategy(userId, strategyType);
            refreshedLinks.putAll(strategyLinks);
        }

        return refreshedLinks;
    }
}

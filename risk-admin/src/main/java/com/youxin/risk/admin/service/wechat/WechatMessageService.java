package com.youxin.risk.admin.service.wechat;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.model.subscription.MonitorConfig;
import com.youxin.risk.admin.model.subscription.StrategyMonitorMapping;
import com.youxin.risk.admin.model.subscription.SubscriptionRecord;
import com.youxin.risk.admin.tools.wechat.QwClient;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 企业微信消息服务
 * 
 * <AUTHOR> Assistant
 */
@Service
public class WechatMessageService {

    private static final Logger logger = LoggerFactory.getLogger(WechatMessageService.class);

    @Autowired
    private QwClient qwClient;

    @Value("${qw.agent.id:1000146}")
    private Integer agentId;

    private static final String SEND_MESSAGE_URL = "https://qyapi.weixin.qq.com/cgi-bin/message/send";

    /**
     * 发送订阅成功通知
     * 
     * @param userId 用户ID
     * @param mapping 策略监控映射
     * @return 是否发送成功
     */
    public boolean sendSubscriptionSuccessNotification(String userId, StrategyMonitorMapping mapping) {
        try {
            LoggerProxy.info("sendSubscriptionSuccessNotification", logger, 
                    "开始发送订阅成功通知, userId={}, strategyName={}", userId, mapping.getName());

            String accessToken = qwClient.getAccessToken();
            if (accessToken == null) {
                LoggerProxy.error("sendSubscriptionSuccessNotification", logger, "获取企业微信access_token失败");
                return false;
            }

            // 构建消息内容
            StringBuilder content = new StringBuilder();
            content.append("**【订阅成功】新策略已自动订阅**\n\n");
            content.append("您上线的 \"").append(mapping.getName()).append("\" 已为您自动订阅核心监控。\n\n");
            content.append("> **后续安排**\n");
            content.append("> 系统将从现在开始，**每60分钟**为您推送一次监控大盘链接。\n\n");

            // 添加监控链接
            if (mapping.getMonitors() != null && !mapping.getMonitors().isEmpty()) {
                for (MonitorConfig monitor : mapping.getMonitors()) {
                    content.append("[").append(monitor.getName()).append("](").append(monitor.getUrl()).append(")\n");
                }
            }

            // 构建请求体
            JSONObject message = new JSONObject();
            message.put("touser", userId);
            message.put("msgtype", "markdown");
            message.put("agentid", agentId);

            JSONObject markdown = new JSONObject();
            markdown.put("content", content.toString());
            message.put("markdown", markdown);

            // 发送消息
            String url = SEND_MESSAGE_URL + "?access_token=" + accessToken;
            String response = SyncHTTPRemoteAPI.postJson(url, message.toJSONString(), 10000);

            LoggerProxy.info("sendSubscriptionSuccessNotification", logger, 
                    "发送订阅成功通知完成, userId={}, response={}", userId, response);

            // 检查响应
            JSONObject responseJson = JSONObject.parseObject(response);
            return responseJson.getIntValue("errcode") == 0;

        } catch (Exception e) {
            LoggerProxy.error("sendSubscriptionSuccessNotification", logger, 
                    "发送订阅成功通知失败, userId=" + userId, e);
            return false;
        }
    }

    /**
     * 发送定时监控推送
     * 
     * @param userSubscriptions 用户订阅记录映射（按用户ID分组）
     * @param strategyMappings 策略监控映射
     * @return 发送成功的用户数
     */
    public int sendScheduledMonitorPush(Map<String, List<SubscriptionRecord>> userSubscriptions, 
                                       Map<String, StrategyMonitorMapping> strategyMappings) {
        int successCount = 0;
        
        for (Map.Entry<String, List<SubscriptionRecord>> entry : userSubscriptions.entrySet()) {
            String userId = entry.getKey();
            List<SubscriptionRecord> records = entry.getValue();
            
            try {
                boolean sent = sendScheduledPushToUser(userId, records, strategyMappings);
                if (sent) {
                    successCount++;
                }
            } catch (Exception e) {
                LoggerProxy.error("sendScheduledMonitorPush", logger, 
                        "发送定时推送失败, userId=" + userId, e);
            }
        }
        
        return successCount;
    }

    /**
     * 向单个用户发送定时推送
     * 
     * @param userId 用户ID
     * @param records 订阅记录列表
     * @param strategyMappings 策略监控映射
     * @return 是否发送成功
     */
    private boolean sendScheduledPushToUser(String userId, List<SubscriptionRecord> records, 
                                          Map<String, StrategyMonitorMapping> strategyMappings) {
        try {
            String accessToken = qwClient.getAccessToken();
            if (accessToken == null) {
                LoggerProxy.error("sendScheduledPushToUser", logger, "获取企业微信access_token失败");
                return false;
            }

            // 按策略类型分组
            Map<String, List<SubscriptionRecord>> strategyGroups = records.stream()
                    .collect(Collectors.groupingBy(SubscriptionRecord::getStrategyType));

            // 构建消息内容
            StringBuilder content = new StringBuilder();
            content.append("**【定时监控推送】**\n\n");

            for (Map.Entry<String, List<SubscriptionRecord>> strategyEntry : strategyGroups.entrySet()) {
                String strategyType = strategyEntry.getKey();
                List<SubscriptionRecord> strategyRecords = strategyEntry.getValue();
                
                StrategyMonitorMapping mapping = strategyMappings.get(strategyType);
                if (mapping == null) {
                    continue;
                }

                content.append("**").append(mapping.getName()).append("** (每60分钟)\n");
                
                for (SubscriptionRecord record : strategyRecords) {
                    MonitorConfig monitor = findMonitorById(mapping.getMonitors(), record.getMonitorId());
                    if (monitor != null) {
                        int currentPush = record.getSentPushes() + 1;
                        content.append("[• ").append(monitor.getName())
                               .append(" (第").append(currentPush).append("/").append(record.getTotalPushes()).append("次)")
                               .append("](").append(monitor.getUrl()).append(")\n");
                    }
                }
                content.append("\n");
            }

            // 构建请求体
            JSONObject message = new JSONObject();
            message.put("touser", userId);
            message.put("msgtype", "markdown");
            message.put("agentid", agentId);

            JSONObject markdown = new JSONObject();
            markdown.put("content", content.toString());
            message.put("markdown", markdown);

            // 发送消息
            String url = SEND_MESSAGE_URL + "?access_token=" + accessToken;
            String response = SyncHTTPRemoteAPI.postJson(url, message.toJSONString(), 10000);

            LoggerProxy.info("sendScheduledPushToUser", logger, 
                    "发送定时推送完成, userId={}, recordCount={}, response={}", 
                    userId, records.size(), response);

            // 检查响应
            JSONObject responseJson = JSONObject.parseObject(response);
            return responseJson.getIntValue("errcode") == 0;

        } catch (Exception e) {
            LoggerProxy.error("sendScheduledPushToUser", logger, 
                    "发送定时推送失败, userId=" + userId, e);
            return false;
        }
    }

    /**
     * 根据监控ID查找监控配置
     * 
     * @param monitors 监控配置列表
     * @param monitorId 监控ID
     * @return 监控配置
     */
    private MonitorConfig findMonitorById(List<MonitorConfig> monitors, String monitorId) {
        if (monitors == null || monitorId == null) {
            return null;
        }
        
        return monitors.stream()
                .filter(monitor -> monitorId.equals(monitor.getId()))
                .findFirst()
                .orElse(null);
    }
}

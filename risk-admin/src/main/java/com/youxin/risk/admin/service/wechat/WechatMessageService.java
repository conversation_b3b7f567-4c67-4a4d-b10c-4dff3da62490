package com.youxin.risk.admin.service.wechat;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.model.subscription.MonitorConfig;
import com.youxin.risk.admin.model.subscription.StrategyMonitorMapping;
import com.youxin.risk.admin.model.subscription.SubscriptionRecord;
import com.youxin.risk.admin.service.subscription.StrategyMonitorConfigService;
import com.youxin.risk.admin.tools.wechat.QwClient;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 企业微信消息服务
 * 
 * <AUTHOR> Assistant
 */
@Service
public class WechatMessageService {

    private static final Logger logger = LoggerFactory.getLogger(WechatMessageService.class);

    @Autowired
    private QwClient qwClient;

    @Autowired
    private StrategyMonitorConfigService strategyMonitorConfigService;

    @Value("${qw.agent.id:1000146}")
    private Integer agentId;

    @Value("${admin.base.url:http://localhost:8080}")
    private String adminBaseUrl;

    private static final String SEND_MESSAGE_URL = "https://qyapi.weixin.qq.com/cgi-bin/message/send";

    /**
     * 发送订阅成功通知
     * 
     * @param userId 用户ID
     * @param mapping 策略监控映射
     * @return 是否发送成功
     */
    public boolean sendSubscriptionSuccessNotification(String userId, StrategyMonitorMapping mapping) {
        try {
            LoggerProxy.info("sendSubscriptionSuccessNotification", logger, 
                    "开始发送订阅成功通知, userId={}, strategyName={}", userId, mapping.getName());

            String accessToken = qwClient.getAccessToken();
            if (accessToken == null) {
                LoggerProxy.error("sendSubscriptionSuccessNotification", logger, "获取企业微信access_token失败");
                return false;
            }

            // 构建消息内容
            StringBuilder content = new StringBuilder();
            content.append("**【订阅成功】新策略已自动订阅**\n\n");
            content.append("您上线的 \"").append(mapping.getName()).append("\" 已为您自动订阅核心监控。\n\n");
            content.append("> **后续安排**\n");

            // 显示推送频率信息
            int defaultFrequency = 60;
            int strategyFrequency = mapping.getEffectiveFrequencyMinutes(defaultFrequency);
            content.append("> 系统将从现在开始，为您推送监控大盘链接。\n");
            content.append("> 推送频率：**每").append(strategyFrequency).append("分钟**\n\n");

            // 添加监控链接
            if (mapping.getMonitors() != null && !mapping.getMonitors().isEmpty()) {
                for (MonitorConfig monitor : mapping.getMonitors()) {
                    String monitorLink = strategyMonitorConfigService.generateMonitorLink(monitor);
                    if (StringUtils.isNotEmpty(monitorLink)) {
                        // 显示监控项的推送频率（如果与策略级别不同）
                        int monitorFrequency = monitor.getEffectiveFrequencyMinutes(strategyFrequency);
                        if (monitorFrequency != strategyFrequency) {
                            content.append("[").append(monitor.getName())
                                   .append(" (每").append(monitorFrequency).append("分钟)")
                                   .append("](").append(monitorLink).append(")\n");
                        } else {
                            content.append("[").append(monitor.getName()).append("](").append(monitorLink).append(")\n");
                        }
                    } else {
                        content.append("• ").append(monitor.getName()).append(" (链接生成失败)\n");
                    }
                }
            }

            // 添加刷新链接功能说明
            content.append("\n> **链接刷新**\n");
            content.append("> 如果监控链接失效，请点击 [刷新监控链接](").append(adminBaseUrl)
                   .append("/admin/strategy/subscription/refresh-links?userId=").append(userId)
                   .append("&strategyType=").append(mapping.getName()).append(") 获取最新链接。\n");

            // 构建请求体
            JSONObject message = new JSONObject();
            message.put("touser", userId);
            message.put("msgtype", "markdown");
            message.put("agentid", agentId);

            JSONObject markdown = new JSONObject();
            markdown.put("content", content.toString());
            message.put("markdown", markdown);

            // 发送消息
            String url = SEND_MESSAGE_URL + "?access_token=" + accessToken;
            String response = SyncHTTPRemoteAPI.postJson(url, message.toJSONString(), 10000);

            LoggerProxy.info("sendSubscriptionSuccessNotification", logger, 
                    "发送订阅成功通知完成, userId={}, response={}", userId, response);

            // 检查响应
            JSONObject responseJson = JSONObject.parseObject(response);
            return responseJson.getIntValue("errcode") == 0;

        } catch (Exception e) {
            LoggerProxy.error("sendSubscriptionSuccessNotification", logger, 
                    "发送订阅成功通知失败, userId=" + userId, e);
            return false;
        }
    }

    /**
     * 发送定时监控推送
     * 
     * @param userSubscriptions 用户订阅记录映射（按用户ID分组）
     * @param strategyMappings 策略监控映射
     * @return 发送成功的用户数
     */
    public int sendScheduledMonitorPush(Map<String, List<SubscriptionRecord>> userSubscriptions, 
                                       Map<String, StrategyMonitorMapping> strategyMappings) {
        int successCount = 0;
        
        for (Map.Entry<String, List<SubscriptionRecord>> entry : userSubscriptions.entrySet()) {
            String userId = entry.getKey();
            List<SubscriptionRecord> records = entry.getValue();
            
            try {
                boolean sent = sendScheduledPushToUser(userId, records, strategyMappings);
                if (sent) {
                    successCount++;
                }
            } catch (Exception e) {
                LoggerProxy.error("sendScheduledMonitorPush", logger, 
                        "发送定时推送失败, userId=" + userId, e);
            }
        }
        
        return successCount;
    }

    /**
     * 向单个用户发送定时推送
     * 
     * @param userId 用户ID
     * @param records 订阅记录列表
     * @param strategyMappings 策略监控映射
     * @return 是否发送成功
     */
    private boolean sendScheduledPushToUser(String userId, List<SubscriptionRecord> records, 
                                          Map<String, StrategyMonitorMapping> strategyMappings) {
        try {
            String accessToken = qwClient.getAccessToken();
            if (accessToken == null) {
                LoggerProxy.error("sendScheduledPushToUser", logger, "获取企业微信access_token失败");
                return false;
            }

            // 按策略类型分组
            Map<String, List<SubscriptionRecord>> strategyGroups = records.stream()
                    .collect(Collectors.groupingBy(SubscriptionRecord::getStrategyType));

            // 构建消息内容
            StringBuilder content = new StringBuilder();
            content.append("**【定时监控推送】**\n\n");

            for (Map.Entry<String, List<SubscriptionRecord>> strategyEntry : strategyGroups.entrySet()) {
                String strategyType = strategyEntry.getKey();
                List<SubscriptionRecord> strategyRecords = strategyEntry.getValue();
                
                StrategyMonitorMapping mapping = strategyMappings.get(strategyType);
                if (mapping == null) {
                    continue;
                }

                // 显示策略级别的推送频率
                int defaultFrequency = 60;
                int strategyFrequency = mapping.getEffectiveFrequencyMinutes(defaultFrequency);
                content.append("**").append(mapping.getName()).append("** (每").append(strategyFrequency).append("分钟)\n");
                
                for (SubscriptionRecord record : strategyRecords) {
                    MonitorConfig monitor = findMonitorById(mapping.getMonitors(), record.getMonitorId());
                    if (monitor != null) {
                        int currentPush = record.getSentPushes() + 1;
                        String monitorLink = strategyMonitorConfigService.generateMonitorLink(monitor);

                        if (StringUtils.isNotEmpty(monitorLink)) {
                            content.append("[• ").append(monitor.getName())
                                   .append(" (第").append(currentPush).append("/").append(record.getTotalPushes()).append("次)")
                                   .append("](").append(monitorLink).append(")\n");
                        } else {
                            content.append("• ").append(monitor.getName())
                                   .append(" (第").append(currentPush).append("/").append(record.getTotalPushes()).append("次)")
                                   .append(" - 链接生成失败\n");
                        }
                    }
                }
                content.append("\n");
            }

            // 添加刷新链接功能
            content.append("> **链接刷新**\n");
            content.append("> 如果监控链接失效，请点击 [刷新监控链接](").append(adminBaseUrl)
                   .append("/admin/strategy/subscription/refresh-links?userId=").append(userId)
                   .append(") 获取最新链接。\n");

            // 构建请求体
            JSONObject message = new JSONObject();
            message.put("touser", userId);
            message.put("msgtype", "markdown");
            message.put("agentid", agentId);

            JSONObject markdown = new JSONObject();
            markdown.put("content", content.toString());
            message.put("markdown", markdown);

            // 发送消息
            String url = SEND_MESSAGE_URL + "?access_token=" + accessToken;
            String response = SyncHTTPRemoteAPI.postJson(url, message.toJSONString(), 10000);

            LoggerProxy.info("sendScheduledPushToUser", logger,
                    "发送定时推送完成, userId={}, recordCount={}, response={}",
                    userId, records.size(), response);

            // 检查响应
            JSONObject responseJson = JSONObject.parseObject(response);
            return responseJson.getIntValue("errcode") == 0;

        } catch (Exception e) {
            LoggerProxy.error("sendScheduledPushToUser", logger,
                    "发送定时推送失败, userId=" + userId, e);
            return false;
        }
    }

    /**
     * 发送刷新后的监控链接
     *
     * @param userId 用户ID
     * @param refreshedLinks 刷新后的链接映射
     * @return 是否发送成功
     */
    public boolean sendRefreshedMonitorLinks(String userId, Map<String, String> refreshedLinks) {
        try {
            LoggerProxy.info("sendRefreshedMonitorLinks", logger,
                    "开始发送刷新后的监控链接, userId={}, linkCount={}", userId, refreshedLinks.size());

            String accessToken = qwClient.getAccessToken();
            if (accessToken == null) {
                LoggerProxy.error("sendRefreshedMonitorLinks", logger, "获取企业微信access_token失败");
                return false;
            }

            // 构建消息内容
            StringBuilder content = new StringBuilder();
            content.append("**【监控链接已刷新】**\n\n");
            content.append("您的监控链接已更新为最新版本：\n\n");

            for (Map.Entry<String, String> entry : refreshedLinks.entrySet()) {
                String monitorName = entry.getKey();
                String link = entry.getValue();
                if (StringUtils.isNotEmpty(link)) {
                    content.append("[• ").append(monitorName).append("](").append(link).append(")\n");
                } else {
                    content.append("• ").append(monitorName).append(" - 链接刷新失败\n");
                }
            }

            content.append("\n> **提示**: 新链接在多端查看时具有更好的稳定性。");

            // 构建请求体
            JSONObject message = new JSONObject();
            message.put("touser", userId);
            message.put("msgtype", "markdown");
            message.put("agentid", agentId);

            JSONObject markdown = new JSONObject();
            markdown.put("content", content.toString());
            message.put("markdown", markdown);

            // 发送消息
            String url = SEND_MESSAGE_URL + "?access_token=" + accessToken;
            String response = SyncHTTPRemoteAPI.postJson(url, message.toJSONString(), 10000);

            LoggerProxy.info("sendRefreshedMonitorLinks", logger,
                    "发送刷新链接完成, userId={}, response={}", userId, response);

            // 检查响应
            JSONObject responseJson = JSONObject.parseObject(response);
            return responseJson.getIntValue("errcode") == 0;

        } catch (Exception e) {
            LoggerProxy.error("sendRefreshedMonitorLinks", logger,
                    "发送刷新链接失败, userId=" + userId, e);
            return false;
        }
    }

    /**
     * 根据监控ID查找监控配置
     * 
     * @param monitors 监控配置列表
     * @param monitorId 监控ID
     * @return 监控配置
     */
    private MonitorConfig findMonitorById(List<MonitorConfig> monitors, String monitorId) {
        if (monitors == null || monitorId == null) {
            return null;
        }
        
        return monitors.stream()
                .filter(monitor -> monitorId.equals(monitor.getId()))
                .findFirst()
                .orElse(null);
    }
}

package com.youxin.risk.admin.service.subscription;

import com.youxin.risk.admin.dao.admin.SubscriptionRecordMapper;
import com.youxin.risk.admin.model.subscription.MonitorConfig;
import com.youxin.risk.admin.model.subscription.StrategyMonitorMapping;
import com.youxin.risk.admin.model.subscription.SubscriptionRecord;
import com.youxin.risk.admin.service.wechat.WechatMessageService;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 策略订阅服务
 * 

 */
@Service
public class StrategySubscriptionService {

    private static final Logger logger = LoggerFactory.getLogger(StrategySubscriptionService.class);

    @Autowired
    private SubscriptionRecordMapper subscriptionRecordMapper;

    @Autowired
    private StrategyMonitorConfigService strategyMonitorConfigService;

    @Autowired
    private WechatMessageService wechatMessageService;

    /**
     * 创建策略上线订阅
     * 
     * @param userId 用户ID
     * @param strategyType 策略类型
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createStrategySubscription(String userId, String strategyType) {
        try {
            LoggerProxy.info("createStrategySubscription", logger, "开始创建策略订阅, userId={}, strategyType={}", 
                    userId, strategyType);

            // 1. 获取策略监控配置
            StrategyMonitorMapping mapping = strategyMonitorConfigService.getStrategyMonitorMapping(strategyType);
            if (mapping == null || mapping.getMonitors() == null || mapping.getMonitors().isEmpty()) {
                LoggerProxy.warn("createStrategySubscription", logger, "策略类型未配置监控项, strategyType={}", strategyType);
                return false;
            }

            // 2. 创建订阅记录
            List<SubscriptionRecord> records = new ArrayList<>();
            Date now = new Date();

            // 获取默认配置
            int defaultFrequency = 60; // 默认60分钟
            int defaultTotalPushes = 3; // 默认推送3次

            for (MonitorConfig monitor : mapping.getMonitors()) {
                // 检查是否已存在订阅记录
                SubscriptionRecord existingRecord = subscriptionRecordMapper.findByUserAndStrategyAndMonitor(
                        userId, strategyType, monitor.getId());

                if (existingRecord != null) {
                    LoggerProxy.info("createStrategySubscription", logger,
                            "订阅记录已存在, userId={}, strategyType={}, monitorId={}",
                            userId, strategyType, monitor.getId());
                    continue;
                }

                SubscriptionRecord record = new SubscriptionRecord();
                record.setUserId(userId);
                record.setStrategyType(strategyType);
                record.setMonitorId(monitor.getId());

                // 设置推送频率和次数（优先级：监控项 > 策略 > 默认值）
                int effectiveFrequency = monitor.getEffectiveFrequencyMinutes(
                        mapping.getEffectiveFrequencyMinutes(defaultFrequency));
                int effectiveTotalPushes = monitor.getEffectiveTotalPushes(
                        mapping.getEffectiveTotalPushes(defaultTotalPushes));

                record.setFrequencyMinutes(effectiveFrequency);
                record.setTotalPushes(effectiveTotalPushes);
                record.setSentPushes(0);
                record.setIsActive(1);

                record.setCreateTime(now);
                record.setUpdateTime(now);
                records.add(record);

                LoggerProxy.info("createStrategySubscription", logger,
                        "创建订阅记录, userId={}, strategyType={}, monitorId={}, frequency={}, totalPushes={}",
                        userId, strategyType, monitor.getId(), effectiveFrequency, effectiveTotalPushes);
            }

            if (!records.isEmpty()) {
                int insertCount = subscriptionRecordMapper.batchInsert(records);
                LoggerProxy.info("createStrategySubscription", logger, 
                        "批量插入订阅记录成功, userId={}, strategyType={}, insertCount={}", 
                        userId, strategyType, insertCount);
            }

            // 3. 发送订阅成功通知
            boolean notificationSent = wechatMessageService.sendSubscriptionSuccessNotification(userId, mapping);
            if (!notificationSent) {
                LoggerProxy.warn("createStrategySubscription", logger, 
                        "发送订阅成功通知失败, userId={}, strategyType={}", userId, strategyType);
            }

            LoggerProxy.info("createStrategySubscription", logger, 
                    "创建策略订阅完成, userId={}, strategyType={}, recordCount={}", 
                    userId, strategyType, records.size());
            return true;

        } catch (Exception e) {
            LoggerProxy.error("createStrategySubscription", logger, 
                    "创建策略订阅失败, userId=" + userId + ", strategyType=" + strategyType, e);
            throw new RuntimeException("创建策略订阅失败", e);
        }
    }

    /**
     * 获取用户的订阅记录
     * 
     * @param userId 用户ID
     * @param strategyType 策略类型
     * @return 订阅记录列表
     */
    public List<SubscriptionRecord> getUserSubscriptions(String userId, String strategyType) {
        try {
            return subscriptionRecordMapper.findByUserAndStrategy(userId, strategyType);
        } catch (Exception e) {
            LoggerProxy.error("getUserSubscriptions", logger, 
                    "获取用户订阅记录失败, userId=" + userId + ", strategyType=" + strategyType, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取需要推送的订阅记录
     * 
     * @return 需要推送的订阅记录列表
     */
    public List<SubscriptionRecord> getPendingSubscriptions() {
        try {
            return subscriptionRecordMapper.findPendingSubscriptions();
        } catch (Exception e) {
            LoggerProxy.error("getPendingSubscriptions", logger, "获取待推送订阅记录失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 批量更新推送状态
     * 
     * @param recordIds 记录ID列表
     * @return 更新的记录数
     */
    public int batchUpdatePushStatus(List<Long> recordIds) {
        try {
            if (recordIds == null || recordIds.isEmpty()) {
                return 0;
            }
            return subscriptionRecordMapper.batchUpdatePushStatus(recordIds);
        } catch (Exception e) {
            LoggerProxy.error("batchUpdatePushStatus", logger, "批量更新推送状态失败, recordIds=" + recordIds, e);
            return 0;
        }
    }
}

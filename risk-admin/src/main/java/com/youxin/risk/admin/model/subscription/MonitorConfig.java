package com.youxin.risk.admin.model.subscription;

/**
 * 监控配置实体类
 *
 * <AUTHOR> Assistant
 */
public class MonitorConfig {

    /**
     * 监控项唯一ID (对应 dashboardName)
     */
    private String id;

    /**
     * 监控项名称
     */
    private String name;

    /**
     * 监控项URL (静态URL，向后兼容)
     */
    private String url;

    /**
     * 事件代码 (用于动态生成监控链接)
     */
    private String eventCode;

    /**
     * 是否使用动态链接生成
     */
    private Boolean useDynamicLink;

    public MonitorConfig() {
        this.useDynamicLink = false;
    }

    public MonitorConfig(String id, String name, String url) {
        this.id = id;
        this.name = name;
        this.url = url;
        this.useDynamicLink = false;
    }

    public MonitorConfig(String id, String name, String eventCode, Boolean useDynamicLink) {
        this.id = id;
        this.name = name;
        this.eventCode = eventCode;
        this.useDynamicLink = useDynamicLink != null ? useDynamicLink : false;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public Boolean getUseDynamicLink() {
        return useDynamicLink;
    }

    public void setUseDynamicLink(Boolean useDynamicLink) {
        this.useDynamicLink = useDynamicLink;
    }

    /**
     * 判断是否使用动态链接
     *
     * @return true if using dynamic link generation
     */
    public boolean isDynamicLinkEnabled() {
        return useDynamicLink != null && useDynamicLink && eventCode != null && !eventCode.trim().isEmpty();
    }

    @Override
    public String toString() {
        return "MonitorConfig{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", url='" + url + '\'' +
                ", eventCode='" + eventCode + '\'' +
                ", useDynamicLink=" + useDynamicLink +
                '}';
    }
}

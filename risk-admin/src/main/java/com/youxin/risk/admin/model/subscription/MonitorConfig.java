package com.youxin.risk.admin.model.subscription;

/**
 * 监控配置实体类
 *
 * <AUTHOR> Assistant
 */
public class MonitorConfig {

    /**
     * 监控项唯一ID (对应 dashboardName)
     */
    private String id;

    /**
     * 监控项名称
     */
    private String name;

    /**
     * 监控项URL (静态URL，向后兼容)
     */
    private String url;

    /**
     * 事件代码 (用于动态生成监控链接)
     */
    private String eventCode;

    /**
     * 是否使用动态链接生成
     */
    private Boolean useDynamicLink;

    /**
     * 推送频率(分钟) - 监控项级别配置
     */
    private Integer frequencyMinutes;

    /**
     * 总推送次数 - 监控项级别配置
     */
    private Integer totalPushes;

    public MonitorConfig() {
        this.useDynamicLink = false;
    }

    public MonitorConfig(String id, String name, String url) {
        this.id = id;
        this.name = name;
        this.url = url;
        this.useDynamicLink = false;
    }

    public MonitorConfig(String id, String name, String eventCode, Boolean useDynamicLink) {
        this.id = id;
        this.name = name;
        this.eventCode = eventCode;
        this.useDynamicLink = useDynamicLink != null ? useDynamicLink : false;
    }

    public MonitorConfig(String id, String name, String eventCode, Boolean useDynamicLink,
                        Integer frequencyMinutes, Integer totalPushes) {
        this.id = id;
        this.name = name;
        this.eventCode = eventCode;
        this.useDynamicLink = useDynamicLink != null ? useDynamicLink : false;
        this.frequencyMinutes = frequencyMinutes;
        this.totalPushes = totalPushes;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public Boolean getUseDynamicLink() {
        return useDynamicLink;
    }

    public void setUseDynamicLink(Boolean useDynamicLink) {
        this.useDynamicLink = useDynamicLink;
    }

    public Integer getFrequencyMinutes() {
        return frequencyMinutes;
    }

    public void setFrequencyMinutes(Integer frequencyMinutes) {
        this.frequencyMinutes = frequencyMinutes;
    }

    public Integer getTotalPushes() {
        return totalPushes;
    }

    public void setTotalPushes(Integer totalPushes) {
        this.totalPushes = totalPushes;
    }

    /**
     * 判断是否使用动态链接
     *
     * @return true if using dynamic link generation
     */
    public boolean isDynamicLinkEnabled() {
        return useDynamicLink != null && useDynamicLink && eventCode != null && !eventCode.trim().isEmpty();
    }

    /**
     * 获取有效的推送频率（分钟）
     * 优先使用监控项级别配置，如果没有则使用默认值
     *
     * @param defaultFrequency 默认频率
     * @return 有效的推送频率
     */
    public int getEffectiveFrequencyMinutes(int defaultFrequency) {
        return frequencyMinutes != null && frequencyMinutes > 0 ? frequencyMinutes : defaultFrequency;
    }

    /**
     * 获取有效的总推送次数
     * 优先使用监控项级别配置，如果没有则使用默认值
     *
     * @param defaultTotalPushes 默认总推送次数
     * @return 有效的总推送次数
     */
    public int getEffectiveTotalPushes(int defaultTotalPushes) {
        return totalPushes != null && totalPushes > 0 ? totalPushes : defaultTotalPushes;
    }

    @Override
    public String toString() {
        return "MonitorConfig{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", url='" + url + '\'' +
                ", eventCode='" + eventCode + '\'' +
                ", useDynamicLink=" + useDynamicLink +
                ", frequencyMinutes=" + frequencyMinutes +
                ", totalPushes=" + totalPushes +
                '}';
    }
}

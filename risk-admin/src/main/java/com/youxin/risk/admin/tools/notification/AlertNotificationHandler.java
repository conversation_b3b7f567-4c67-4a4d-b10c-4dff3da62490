package com.youxin.risk.admin.tools.notification;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.admin.service.subscription.StrategySubscriptionService;
import com.youxin.risk.admin.utils.DateUtils;
import com.youxin.risk.admin.utils.UserInfoUtil;
import com.youxin.risk.commons.constants.AlertExtendOperationTypeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.SLSLinkGenerator;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AlertNotificationHandler {

    private static final Logger logger = LoggerFactory.getLogger(AlertNotificationHandler.class);

    @Value("${alert.base.url}")
    private String alertUrl;
    private static final String ALERT_MSG_API_URL = "/alert/api/event/riskAlert/v1";

    @Autowired
    private StrategySubscriptionService strategySubscriptionService;

    // 规则集上线通知
    public void sendRuleOnlineNotification(String ruleKey, Integer version, String modifyUser) {
        JSONObject msg = new JSONObject(true);
        msg.put("规则集名称", ruleKey);
        msg.put("规则集版本", version);
        msg.put("上线人", modifyUser);
        msg.put("上线时间", DateUtils.getTime());

        sendNotification("sendOnlineNotification", msg, ALERT_MSG_API_URL);
    }

    // 策略上线通知
    public void sendStrategyOnlineToAlert(String strategyType, String eventCode, String userName) {
        JSONObject msg = new JSONObject(true);
        msg.put("策略类型", strategyType);
        msg.put("上线人员", userName);
        msg.put("上线时间", DateUtils.getTime());
        if(StringUtils.isNotEmpty(eventCode)
                && SLSLinkGenerator.isEventCodeConfigured(eventCode)){
            JSONObject extendOp = new JSONObject();
            extendOp.put("type", AlertExtendOperationTypeEnum.SLS_DASHBOARD_LINK.getCode());

            JSONObject params = new JSONObject();
            params.put("eventCode", eventCode);
            extendOp.put("params", params);

            msg.put("扩展操作", extendOp);
        }

        sendNotification("sendOnlineToAlert", msg, ALERT_MSG_API_URL);

        // 创建策略监控自动订阅
        createStrategyMonitorSubscription(strategyType, userName);
    }

    /**
     * 创建策略监控自动订阅
     *
     * @param strategyType 策略类型
     * @param userName 用户名
     */
    private void createStrategyMonitorSubscription(String strategyType, String userName) {
        try {
            LoggerProxy.info("createStrategyMonitorSubscription", logger,
                    "开始创建策略监控订阅, strategyType={}, userName={}", strategyType, userName);

            boolean success = strategySubscriptionService.createStrategySubscription(userName, strategyType);

            if (success) {
                LoggerProxy.info("createStrategyMonitorSubscription", logger,
                        "策略监控订阅创建成功, strategyType={}, userName={}", strategyType, userName);
            } else {
                LoggerProxy.warn("createStrategyMonitorSubscription", logger,
                        "策略监控订阅创建失败, strategyType={}, userName={}", strategyType, userName);
            }
        } catch (Exception e) {
            LoggerProxy.error("createStrategyMonitorSubscription", logger,
                    "创建策略监控订阅异常, strategyType=" + strategyType + ", userName=" + userName, e);
        }
    }

    // 规则集解绑通知
    public void sendRuleUnbindingNotification(String ruleKey, Integer version, String node) {
        JSONObject msg = new JSONObject(true);
        msg.put("规则集名称", ruleKey);
        msg.put("规则集版本", version);
        msg.put("解绑节点", node);
        msg.put("解绑人", UserInfoUtil.getUsername());
        msg.put("解绑时间", DateUtils.getTime());

        sendNotification("sendUnbindingNotification", msg, ALERT_MSG_API_URL);
    }

    // 规则集绑定通知
    public void sendRuleBindingNotification(String ruleKey, Integer version, List<String> bindNode) {
        JSONObject msg = new JSONObject(true);
        msg.put("规则集名称", ruleKey);
        msg.put("规则集版本", version);
        msg.put("绑定节点", bindNode);
        msg.put("绑定人", UserInfoUtil.getUsername());
        msg.put("绑定时间", DateUtils.getTime());

        sendNotification("sendBindingNotification", msg, ALERT_MSG_API_URL);
    }

    private void sendNotification(String notificationType, JSONObject msg, String apiUrlSuffix) {
        String logMessage = notificationType + ", msg=" + JSON.toJSONString(msg);
        LoggerProxy.info("sendNotification", logger, logMessage);

        String url = alertUrl;
        if (apiUrlSuffix != null && !apiUrlSuffix.isEmpty()) {
            url += apiUrlSuffix;
        }

        try {
            SyncHTTPRemoteAPI.postJson(url, JSON.toJSONString(msg), 10000);
            LoggerProxy.info("sendNotificationSuccess", logger, logMessage);
        } catch (Exception e) {
            LoggerProxy.error(logger, notificationType + "Error", e);
        }
    }

}
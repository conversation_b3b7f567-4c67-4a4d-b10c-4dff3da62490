package com.youxin.risk.admin.scheduler.xxljob;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.admin.model.subscription.StrategyMonitorMapping;
import com.youxin.risk.admin.model.subscription.SubscriptionRecord;
import com.youxin.risk.admin.service.subscription.StrategyMonitorConfigService;
import com.youxin.risk.admin.service.subscription.StrategySubscriptionService;
import com.youxin.risk.admin.service.wechat.WechatMessageService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 策略监控推送定时任务
 * 
 * <AUTHOR> Assistant
 */
@Component
public class StrategyMonitorPushJob implements XxlJobBase {

    private static final Logger logger = LoggerFactory.getLogger(StrategyMonitorPushJob.class);

    @Autowired
    private StrategySubscriptionService strategySubscriptionService;

    @Autowired
    private StrategyMonitorConfigService strategyMonitorConfigService;

    @Autowired
    private WechatMessageService wechatMessageService;

    @Override
    @XxlJob(value = "StrategyMonitorPushJob")
    public ReturnT<String> execJobHandler(String param) {
        LoggerProxy.info("StrategyMonitorPushJob start", logger, "策略监控推送定时任务开始执行");
        
        try {
            // 1. 获取需要推送的订阅记录
            List<SubscriptionRecord> pendingSubscriptions = strategySubscriptionService.getPendingSubscriptions();
            
            if (pendingSubscriptions.isEmpty()) {
                LoggerProxy.info("StrategyMonitorPushJob", logger, "没有需要推送的订阅记录");
                return ReturnT.SUCCESS;
            }

            LoggerProxy.info("StrategyMonitorPushJob", logger, 
                    "找到需要推送的订阅记录, count={}", pendingSubscriptions.size());

            // 2. 按用户ID分组
            Map<String, List<SubscriptionRecord>> userSubscriptions = pendingSubscriptions.stream()
                    .collect(Collectors.groupingBy(SubscriptionRecord::getUserId));

            // 3. 获取所有策略监控配置
            Map<String, StrategyMonitorMapping> strategyMappings = strategyMonitorConfigService.getAllStrategyMonitorMappings();
            if (strategyMappings == null || strategyMappings.isEmpty()) {
                LoggerProxy.warn("StrategyMonitorPushJob", logger, "策略监控配置为空，跳过推送");
                return ReturnT.SUCCESS;
            }

            // 4. 发送推送消息
            int successCount = wechatMessageService.sendScheduledMonitorPush(userSubscriptions, strategyMappings);
            
            LoggerProxy.info("StrategyMonitorPushJob", logger, 
                    "推送消息完成, totalUsers={}, successUsers={}", userSubscriptions.size(), successCount);

            // 5. 更新推送状态
            List<Long> recordIds = pendingSubscriptions.stream()
                    .map(SubscriptionRecord::getId)
                    .collect(Collectors.toList());
            
            int updateCount = strategySubscriptionService.batchUpdatePushStatus(recordIds);
            
            LoggerProxy.info("StrategyMonitorPushJob", logger, 
                    "更新推送状态完成, totalRecords={}, updatedRecords={}", recordIds.size(), updateCount);

            // 6. 记录执行结果
            String result = String.format("推送完成: 总用户数=%d, 成功用户数=%d, 总记录数=%d, 更新记录数=%d", 
                    userSubscriptions.size(), successCount, recordIds.size(), updateCount);
            
            LoggerProxy.info("StrategyMonitorPushJob end", logger, result);
            
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            LoggerProxy.error("StrategyMonitorPushJob", logger, "策略监控推送定时任务执行失败", e);
            return ReturnT.FAIL;
        }
    }
}

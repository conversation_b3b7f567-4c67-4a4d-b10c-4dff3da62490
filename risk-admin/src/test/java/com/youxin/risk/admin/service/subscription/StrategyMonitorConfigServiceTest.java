package com.youxin.risk.admin.service.subscription;

import com.youxin.risk.admin.model.subscription.MonitorConfig;
import com.youxin.risk.commons.utils.SLSLinkGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * 策略监控配置服务测试类
 * 

 */
@RunWith(MockitoJUnitRunner.class)
public class StrategyMonitorConfigServiceTest {

    @InjectMocks
    private StrategyMonitorConfigService strategyMonitorConfigService;

    private MonitorConfig dynamicMonitor;
    private MonitorConfig staticMonitor;

    @Before
    public void setUp() {
        // 准备测试数据
        dynamicMonitor = new MonitorConfig();
        dynamicMonitor.setId("dashboard-1747904265305-468192");
        dynamicMonitor.setName("动态监控大盘");
        dynamicMonitor.setEventCode("ApiVerify");
        dynamicMonitor.setUseDynamicLink(true);

        staticMonitor = new MonitorConfig();
        staticMonitor.setId("sls-002");
        staticMonitor.setName("静态监控大盘");
        staticMonitor.setUrl("http://sls.example.com/d/xxx/static");
        staticMonitor.setUseDynamicLink(false);
    }

    @Test
    public void testGenerateMonitorLink_DynamicLink_Success() {
        // Mock SLSLinkGenerator
        try (MockedStatic<SLSLinkGenerator> mockedSLS = Mockito.mockStatic(SLSLinkGenerator.class)) {
            String expectedLink = "https://sls.console.aliyun.com/lognext/project/risk-service-logs/dashboard/dashboard-1747904265305-468192?slsRegion=cn-beijing&sls_ticket=test-ticket";
            mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByEventCode("ApiVerify"))
                    .thenReturn(expectedLink);

            // 执行测试
            String result = strategyMonitorConfigService.generateMonitorLink(dynamicMonitor);

            // 验证结果
            assertEquals(expectedLink, result);
            mockedSLS.verify(() -> SLSLinkGenerator.getShareableLinkByEventCode("ApiVerify"));
        }
    }

    @Test
    public void testGenerateMonitorLink_DynamicLink_Fallback() {
        // 设置动态监控也有静态URL作为备用
        dynamicMonitor.setUrl("http://sls.example.com/d/xxx/fallback");

        // Mock SLSLinkGenerator 返回null
        try (MockedStatic<SLSLinkGenerator> mockedSLS = Mockito.mockStatic(SLSLinkGenerator.class)) {
            mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByEventCode("ApiVerify"))
                    .thenReturn(null);

            // 执行测试
            String result = strategyMonitorConfigService.generateMonitorLink(dynamicMonitor);

            // 验证结果 - 应该回退到静态URL
            assertEquals("http://sls.example.com/d/xxx/fallback", result);
            mockedSLS.verify(() -> SLSLinkGenerator.getShareableLinkByEventCode("ApiVerify"));
        }
    }

    @Test
    public void testGenerateMonitorLink_StaticLink() {
        // 执行测试
        String result = strategyMonitorConfigService.generateMonitorLink(staticMonitor);

        // 验证结果
        assertEquals("http://sls.example.com/d/xxx/static", result);
    }

    @Test
    public void testGenerateMonitorLink_NullMonitor() {
        // 执行测试
        String result = strategyMonitorConfigService.generateMonitorLink(null);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testBatchGenerateMonitorLinks() {
        List<MonitorConfig> monitors = Arrays.asList(dynamicMonitor, staticMonitor);

        // Mock SLSLinkGenerator
        try (MockedStatic<SLSLinkGenerator> mockedSLS = Mockito.mockStatic(SLSLinkGenerator.class)) {
            String expectedDynamicLink = "https://sls.console.aliyun.com/dynamic-link";
            mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByEventCode("ApiVerify"))
                    .thenReturn(expectedDynamicLink);

            // 执行测试
            Map<String, String> result = strategyMonitorConfigService.batchGenerateMonitorLinks(monitors);

            // 验证结果
            assertEquals(2, result.size());
            assertEquals(expectedDynamicLink, result.get("dashboard-1747904265305-468192"));
            assertEquals("http://sls.example.com/d/xxx/static", result.get("sls-002"));
        }
    }

    @Test
    public void testRefreshMonitorLink_Success() {
        // Mock SLSLinkGenerator
        try (MockedStatic<SLSLinkGenerator> mockedSLS = Mockito.mockStatic(SLSLinkGenerator.class)) {
            String expectedLink = "https://sls.console.aliyun.com/refreshed-link";
            mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByEventCode("ApiVerify"))
                    .thenReturn(expectedLink);

            // 执行测试
            String result = strategyMonitorConfigService.refreshMonitorLink("ApiVerify");

            // 验证结果
            assertEquals(expectedLink, result);
            mockedSLS.verify(() -> SLSLinkGenerator.getShareableLinkByEventCode("ApiVerify"));
        }
    }

    @Test
    public void testRefreshMonitorLink_EmptyEventCode() {
        // 执行测试
        String result = strategyMonitorConfigService.refreshMonitorLink("");

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testRefreshMonitorLink_NullEventCode() {
        // 执行测试
        String result = strategyMonitorConfigService.refreshMonitorLink(null);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testRefreshMonitorLink_GenerationFailed() {
        // Mock SLSLinkGenerator 返回null
        try (MockedStatic<SLSLinkGenerator> mockedSLS = Mockito.mockStatic(SLSLinkGenerator.class)) {
            mockedSLS.when(() -> SLSLinkGenerator.getShareableLinkByEventCode(anyString()))
                    .thenReturn(null);

            // 执行测试
            String result = strategyMonitorConfigService.refreshMonitorLink("InvalidEventCode");

            // 验证结果
            assertNull(result);
        }
    }

    @Test
    public void testMonitorConfig_IsDynamicLinkEnabled() {
        // 测试动态链接启用
        assertTrue(dynamicMonitor.isDynamicLinkEnabled());

        // 测试静态链接
        assertFalse(staticMonitor.isDynamicLinkEnabled());

        // 测试没有eventCode的情况
        MonitorConfig noEventCodeMonitor = new MonitorConfig();
        noEventCodeMonitor.setUseDynamicLink(true);
        assertFalse(noEventCodeMonitor.isDynamicLinkEnabled());

        // 测试useDynamicLink为false的情况
        MonitorConfig disabledDynamicMonitor = new MonitorConfig();
        disabledDynamicMonitor.setEventCode("TestEvent");
        disabledDynamicMonitor.setUseDynamicLink(false);
        assertFalse(disabledDynamicMonitor.isDynamicLinkEnabled());
    }
}

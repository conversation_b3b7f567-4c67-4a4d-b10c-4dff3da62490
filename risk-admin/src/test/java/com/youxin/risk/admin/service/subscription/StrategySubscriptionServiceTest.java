package com.youxin.risk.admin.service.subscription;

import com.youxin.risk.admin.dao.admin.SubscriptionRecordMapper;
import com.youxin.risk.admin.model.subscription.MonitorConfig;
import com.youxin.risk.admin.model.subscription.StrategyMonitorMapping;
import com.youxin.risk.admin.model.subscription.SubscriptionRecord;
import com.youxin.risk.admin.service.wechat.WechatMessageService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 策略订阅服务测试类
 * 
 * <AUTHOR> Assistant
 */
@RunWith(MockitoJUnitRunner.class)
public class StrategySubscriptionServiceTest {

    @Mock
    private SubscriptionRecordMapper subscriptionRecordMapper;

    @Mock
    private StrategyMonitorConfigService strategyMonitorConfigService;

    @Mock
    private WechatMessageService wechatMessageService;

    @InjectMocks
    private StrategySubscriptionService strategySubscriptionService;

    private StrategyMonitorMapping testMapping;
    private List<MonitorConfig> testMonitors;

    @Before
    public void setUp() {
        // 准备测试数据
        testMonitors = Arrays.asList(
                new MonitorConfig("dashboard-1747904265305-468192", "核心指标大盘", "ApiVerify", true),
                new MonitorConfig("sls-002", "性能监控", "http://sls.example.com/d/xxx/perf")
        );
        
        testMapping = new StrategyMonitorMapping("A类推荐策略", testMonitors);
    }

    @Test
    public void testCreateStrategySubscription_Success() {
        // 准备测试数据
        String userId = "testuser";
        String strategyType = "strategyTypeA";

        // Mock 依赖方法
        when(strategyMonitorConfigService.getStrategyMonitorMapping(strategyType))
                .thenReturn(testMapping);
        when(subscriptionRecordMapper.findByUserAndStrategyAndMonitor(anyString(), anyString(), anyString()))
                .thenReturn(null); // 不存在重复记录
        when(subscriptionRecordMapper.batchInsert(anyList())).thenReturn(2);
        when(wechatMessageService.sendSubscriptionSuccessNotification(userId, testMapping))
                .thenReturn(true);

        // 执行测试
        boolean result = strategySubscriptionService.createStrategySubscription(userId, strategyType);

        // 验证结果
        assertTrue(result);
        verify(strategyMonitorConfigService).getStrategyMonitorMapping(strategyType);
        verify(subscriptionRecordMapper).batchInsert(anyList());
        verify(wechatMessageService).sendSubscriptionSuccessNotification(userId, testMapping);
    }

    @Test
    public void testCreateStrategySubscription_NoMonitorConfig() {
        // 准备测试数据
        String userId = "testuser";
        String strategyType = "unknownStrategy";

        // Mock 依赖方法
        when(strategyMonitorConfigService.getStrategyMonitorMapping(strategyType))
                .thenReturn(null);

        // 执行测试
        boolean result = strategySubscriptionService.createStrategySubscription(userId, strategyType);

        // 验证结果
        assertFalse(result);
        verify(strategyMonitorConfigService).getStrategyMonitorMapping(strategyType);
        verify(subscriptionRecordMapper, never()).batchInsert(anyList());
        verify(wechatMessageService, never()).sendSubscriptionSuccessNotification(anyString(), any());
    }

    @Test
    public void testCreateStrategySubscription_ExistingRecords() {
        // 准备测试数据
        String userId = "testuser";
        String strategyType = "strategyTypeA";
        SubscriptionRecord existingRecord = new SubscriptionRecord();
        existingRecord.setUserId(userId);
        existingRecord.setStrategyType(strategyType);
        existingRecord.setMonitorId("sls-001");

        // Mock 依赖方法
        when(strategyMonitorConfigService.getStrategyMonitorMapping(strategyType))
                .thenReturn(testMapping);
        when(subscriptionRecordMapper.findByUserAndStrategyAndMonitor(userId, strategyType, "sls-001"))
                .thenReturn(existingRecord);
        when(subscriptionRecordMapper.findByUserAndStrategyAndMonitor(userId, strategyType, "sls-002"))
                .thenReturn(null);
        when(subscriptionRecordMapper.batchInsert(anyList())).thenReturn(1);
        when(wechatMessageService.sendSubscriptionSuccessNotification(userId, testMapping))
                .thenReturn(true);

        // 执行测试
        boolean result = strategySubscriptionService.createStrategySubscription(userId, strategyType);

        // 验证结果
        assertTrue(result);
        verify(subscriptionRecordMapper).batchInsert(argThat(records -> records.size() == 1));
    }

    @Test
    public void testGetUserSubscriptions_Success() {
        // 准备测试数据
        String userId = "testuser";
        String strategyType = "strategyTypeA";
        List<SubscriptionRecord> expectedRecords = Arrays.asList(
                createTestRecord(userId, strategyType, "sls-001"),
                createTestRecord(userId, strategyType, "sls-002")
        );

        // Mock 依赖方法
        when(subscriptionRecordMapper.findByUserAndStrategy(userId, strategyType))
                .thenReturn(expectedRecords);

        // 执行测试
        List<SubscriptionRecord> result = strategySubscriptionService.getUserSubscriptions(userId, strategyType);

        // 验证结果
        assertEquals(2, result.size());
        assertEquals(expectedRecords, result);
        verify(subscriptionRecordMapper).findByUserAndStrategy(userId, strategyType);
    }

    @Test
    public void testGetPendingSubscriptions_Success() {
        // 准备测试数据
        List<SubscriptionRecord> expectedRecords = Arrays.asList(
                createTestRecord("user1", "strategyA", "sls-001"),
                createTestRecord("user2", "strategyB", "sls-002")
        );

        // Mock 依赖方法
        when(subscriptionRecordMapper.findPendingSubscriptions()).thenReturn(expectedRecords);

        // 执行测试
        List<SubscriptionRecord> result = strategySubscriptionService.getPendingSubscriptions();

        // 验证结果
        assertEquals(2, result.size());
        assertEquals(expectedRecords, result);
        verify(subscriptionRecordMapper).findPendingSubscriptions();
    }

    @Test
    public void testBatchUpdatePushStatus_Success() {
        // 准备测试数据
        List<Long> recordIds = Arrays.asList(1L, 2L, 3L);

        // Mock 依赖方法
        when(subscriptionRecordMapper.batchUpdatePushStatus(recordIds)).thenReturn(3);

        // 执行测试
        int result = strategySubscriptionService.batchUpdatePushStatus(recordIds);

        // 验证结果
        assertEquals(3, result);
        verify(subscriptionRecordMapper).batchUpdatePushStatus(recordIds);
    }

    @Test
    public void testBatchUpdatePushStatus_EmptyList() {
        // 执行测试
        int result = strategySubscriptionService.batchUpdatePushStatus(new ArrayList<>());

        // 验证结果
        assertEquals(0, result);
        verify(subscriptionRecordMapper, never()).batchUpdatePushStatus(anyList());
    }

    @Test
    public void testBatchUpdatePushStatus_NullList() {
        // 执行测试
        int result = strategySubscriptionService.batchUpdatePushStatus(null);

        // 验证结果
        assertEquals(0, result);
        verify(subscriptionRecordMapper, never()).batchUpdatePushStatus(anyList());
    }

    /**
     * 创建测试用的订阅记录
     */
    private SubscriptionRecord createTestRecord(String userId, String strategyType, String monitorId) {
        SubscriptionRecord record = new SubscriptionRecord();
        record.setId(1L);
        record.setUserId(userId);
        record.setStrategyType(strategyType);
        record.setMonitorId(monitorId);
        return record;
    }
}

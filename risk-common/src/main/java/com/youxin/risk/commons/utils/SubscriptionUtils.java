package com.youxin.risk.commons.utils;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订阅相关工具类
 * 提取可复用的订阅逻辑
 * 
 * <AUTHOR> Assistant
 */
public class SubscriptionUtils {

    /**
     * 构建订阅成功通知的Markdown内容
     * 
     * @param strategyName 策略名称
     * @param monitorLinks 监控链接列表
     * @param frequencyMinutes 推送频率（分钟）
     * @return Markdown内容
     */
    public static String buildSubscriptionSuccessContent(String strategyName, 
                                                        List<Map<String, String>> monitorLinks, 
                                                        int frequencyMinutes) {
        StringBuilder content = new StringBuilder();
        content.append("**【订阅成功】新策略已自动订阅**\n\n");
        content.append("您上线的 \"").append(strategyName).append("\" 已为您自动订阅核心监控。\n\n");
        content.append("> **后续安排**\n");
        content.append("> 系统将从现在开始，**每").append(frequencyMinutes).append("分钟**为您推送一次监控大盘链接。\n\n");

        if (monitorLinks != null && !monitorLinks.isEmpty()) {
            for (Map<String, String> link : monitorLinks) {
                String name = link.get("name");
                String url = link.get("url");
                if (StringUtils.isNotEmpty(name) && StringUtils.isNotEmpty(url)) {
                    content.append("[").append(name).append("](").append(url).append(")\n");
                }
            }
        }

        return content.toString();
    }

    /**
     * 构建定时推送的Markdown内容
     * 
     * @param strategyGroups 按策略分组的监控信息
     * @param frequencyMinutes 推送频率（分钟）
     * @return Markdown内容
     */
    public static String buildScheduledPushContent(Map<String, List<Map<String, Object>>> strategyGroups, 
                                                  int frequencyMinutes) {
        StringBuilder content = new StringBuilder();
        content.append("**【定时监控推送】**\n\n");

        for (Map.Entry<String, List<Map<String, Object>>> entry : strategyGroups.entrySet()) {
            String strategyName = entry.getKey();
            List<Map<String, Object>> monitors = entry.getValue();

            content.append("**").append(strategyName).append("** (每").append(frequencyMinutes).append("分钟)\n");

            for (Map<String, Object> monitor : monitors) {
                String name = (String) monitor.get("name");
                String url = (String) monitor.get("url");
                Integer currentPush = (Integer) monitor.get("currentPush");
                Integer totalPush = (Integer) monitor.get("totalPush");

                if (StringUtils.isNotEmpty(name) && StringUtils.isNotEmpty(url)) {
                    content.append("[• ").append(name);
                    if (currentPush != null && totalPush != null) {
                        content.append(" (第").append(currentPush).append("/").append(totalPush).append("次)");
                    }
                    content.append("](").append(url).append(")\n");
                }
            }
            content.append("\n");
        }

        return content.toString();
    }

    /**
     * 构建企业微信Markdown消息
     * 
     * @param toUser 接收用户
     * @param content Markdown内容
     * @param agentId 应用ID
     * @return 消息JSON对象
     */
    public static JSONObject buildMarkdownMessage(String toUser, String content, Integer agentId) {
        JSONObject message = new JSONObject();
        message.put("touser", toUser);
        message.put("msgtype", "markdown");
        message.put("agentid", agentId);

        JSONObject markdown = new JSONObject();
        markdown.put("content", content);
        message.put("markdown", markdown);

        return message;
    }

    /**
     * 构建企业微信文本消息
     * 
     * @param toUser 接收用户
     * @param content 文本内容
     * @param agentId 应用ID
     * @return 消息JSON对象
     */
    public static JSONObject buildTextMessage(String toUser, String content, Integer agentId) {
        JSONObject message = new JSONObject();
        message.put("touser", toUser);
        message.put("msgtype", "text");
        message.put("agentid", agentId);

        JSONObject text = new JSONObject();
        text.put("content", content);
        message.put("text", text);

        return message;
    }

    /**
     * 验证订阅参数
     * 
     * @param userId 用户ID
     * @param strategyType 策略类型
     * @return 验证结果
     */
    public static boolean validateSubscriptionParams(String userId, String strategyType) {
        return StringUtils.isNotEmpty(userId) && StringUtils.isNotEmpty(strategyType);
    }

    /**
     * 验证监控配置参数
     * 
     * @param monitorId 监控ID
     * @param monitorName 监控名称
     * @param monitorUrl 监控URL
     * @return 验证结果
     */
    public static boolean validateMonitorConfig(String monitorId, String monitorName, String monitorUrl) {
        return StringUtils.isNotEmpty(monitorId) && 
               StringUtils.isNotEmpty(monitorName) && 
               StringUtils.isNotEmpty(monitorUrl);
    }

    /**
     * 格式化推送次数信息
     * 
     * @param currentPush 当前推送次数
     * @param totalPush 总推送次数
     * @return 格式化后的字符串
     */
    public static String formatPushCount(int currentPush, int totalPush) {
        return String.format("第%d/%d次", currentPush, totalPush);
    }

    /**
     * 检查是否需要推送
     *
     * @param sentPushes 已推送次数
     * @param totalPushes 总推送次数
     * @param lastNotifiedTime 上次通知时间（毫秒）
     * @param frequencyMinutes 推送频率（分钟）
     * @return 是否需要推送
     */
    public static boolean shouldPush(int sentPushes, int totalPushes, Long lastNotifiedTime, int frequencyMinutes) {
        // 检查推送次数
        if (sentPushes >= totalPushes) {
            return false;
        }

        // 检查时间间隔
        if (lastNotifiedTime == null) {
            return true; // 首次推送
        }

        long currentTime = System.currentTimeMillis();
        long intervalMillis = frequencyMinutes * 60 * 1000L;
        return (currentTime - lastNotifiedTime) >= intervalMillis;
    }

    /**
     * 验证事件代码配置参数
     *
     * @param eventCode 事件代码
     * @return 验证结果
     */
    public static boolean validateEventCode(String eventCode) {
        return StringUtils.isNotEmpty(eventCode);
    }

    /**
     * 构建刷新链接的URL
     *
     * @param baseUrl 基础URL
     * @param userId 用户ID
     * @param strategyType 策略类型（可选）
     * @return 刷新链接URL
     */
    public static String buildRefreshLinkUrl(String baseUrl, String userId, String strategyType) {
        StringBuilder url = new StringBuilder(baseUrl);
        url.append("/admin/strategy/subscription/refresh-links?userId=").append(userId);

        if (StringUtils.isNotEmpty(strategyType)) {
            url.append("&strategyType=").append(strategyType);
        }

        return url.toString();
    }

    /**
     * 构建监控链接刷新提示内容
     *
     * @param refreshUrl 刷新链接URL
     * @return 提示内容
     */
    public static String buildRefreshLinkHint(String refreshUrl) {
        return String.format("> **链接刷新**\n> 如果监控链接失效，请点击 [刷新监控链接](%s) 获取最新链接。\n", refreshUrl);
    }

    /**
     * 检查监控配置是否使用动态链接
     *
     * @param useDynamicLink 是否使用动态链接标志
     * @param eventCode 事件代码
     * @return 是否使用动态链接
     */
    public static boolean isDynamicLinkEnabled(Boolean useDynamicLink, String eventCode) {
        return useDynamicLink != null && useDynamicLink && StringUtils.isNotEmpty(eventCode);
    }

    /**
     * 按用户ID分组订阅记录
     * 
     * @param records 订阅记录列表
     * @return 按用户ID分组的记录
     */
    public static <T> Map<String, List<T>> groupByUserId(List<T> records, 
                                                        java.util.function.Function<T, String> userIdExtractor) {
        return records.stream().collect(Collectors.groupingBy(userIdExtractor));
    }

    /**
     * 按策略类型分组订阅记录
     * 
     * @param records 订阅记录列表
     * @return 按策略类型分组的记录
     */
    public static <T> Map<String, List<T>> groupByStrategyType(List<T> records, 
                                                              java.util.function.Function<T, String> strategyTypeExtractor) {
        return records.stream().collect(Collectors.groupingBy(strategyTypeExtractor));
    }
}
